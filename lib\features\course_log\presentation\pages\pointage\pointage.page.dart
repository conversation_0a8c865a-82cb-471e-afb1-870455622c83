import 'package:kairos/core/theme/color_schemes.dart';
import 'package:kairos/core/widgets/common/snackbar_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kairos/core/enums/header_enums.dart';
import 'package:kairos/core/widgets/common/empty_message.widget.dart';
import 'package:kairos/core/widgets/indicators/custom_spinner.dart';
import 'package:kairos/core/widgets/inputs/search_bar_sliver.widget.dart';
import 'package:kairos/core/widgets/layout/custom_app_bar.widget.dart';

import 'package:kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart';
import 'package:kairos/core/di/injection_container.dart';
import 'package:kairos/features/course_log/domain/entities/course_log_entity.dart';
import '../../bloc/course_log_cubit.dart';
import '../../bloc/course_log_state.dart';

import 'pointage_widgets/pointage_day_group.widget.dart';

class PointagePage extends StatefulWidget {
  final EtablissementUtilisateur? school;
  final EnfantTuteurEntity? etudiant;

  const PointagePage({
    super.key,
    this.school,
    this.etudiant,
  });

  @override
  State<PointagePage> createState() => _PointagePageState();
}

class _PointagePageState extends State<PointagePage> with SingleTickerProviderStateMixin {
  bool _isSearchBarVisible = false;
  final TextEditingController _searchController = TextEditingController();
  late AnimationController _searchAnimationController;
  List<List<CourseLogEntity>> _filteredPointageData = [];
  List<List<CourseLogEntity>> _allPointageData = [];
  String _searchQuery = '';
  int? _selectedYear;
  late bool isResponsable;

  @override
  void initState() {
    super.initState();
    _searchAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    // Load pointage data on initialization
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadPointageData();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchAnimationController.dispose();
    super.dispose();
  }

  void _toggleSearchBarVisibility() {
    setState(() {
      _isSearchBarVisible = !_isSearchBarVisible;
      if (_isSearchBarVisible) {
        _searchAnimationController.forward();
      } else {
        _searchAnimationController.reverse();
        _searchController.clear();
        _searchQuery = '';
        _filteredPointageData = _filterPointageData(_allPointageData, _searchQuery);
      }
    });
  }

  Future<void> _loadPointageData() async {
    try {
      if (context.mounted) {
        // Load mock pointage data for PRF users
        context.read<CourseLogCubit>().loadMockPointageData();
      }
    } catch (e) {
      debugPrint('PointagePage - Error loading pointage data: $e');
    }
  }

  List<List<CourseLogEntity>> _filterPointageData(List<List<CourseLogEntity>> data, String query) {
    if (query.isEmpty) return data;
    
    return data.map((dayGroup) {
      return dayGroup.where((pointage) {
        return pointage.cours.toLowerCase().contains(query.toLowerCase()) ||
               pointage.professeur.toLowerCase().contains(query.toLowerCase());
      }).toList();
    }).where((dayGroup) => dayGroup.isNotEmpty).toList();
  }

  void _onDateFilterChanged(Map<String, String> dateRange) {
    final year = dateRange['endDate'];
    if (year != null && year.isNotEmpty) {
      setState(() {
        _selectedYear = int.tryParse(year);
      });
      _loadFilteredPointageData();
    }
  }

  void _onClearDateFilter() {
    setState(() {
      _selectedYear = null;
    });
    _loadPointageData();
  }

  Future<void> _loadFilteredPointageData() async {
    if (_selectedYear == null) {
      _loadPointageData();
      return;
    }

    try {
      if (context.mounted) {
        // For PRF users, we'll just load the same mock data regardless of year filter
        // In a real implementation, this would filter by year
        context.read<CourseLogCubit>().loadMockPointageData();
      }
    } catch (e) {
      debugPrint('PointagePage - Error loading filtered pointage data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: BlocConsumer<CourseLogCubit, CourseLogState>(
        listener: (context, state) {
          if (state is CourseLogError) {
            ScaffoldMessenger.of(context).showSnackBar(
              CustomSnackbar(message: state.message).getSnackBar(),
            );
          } else if (state is CourseLogLoaded) {
            setState(() {
              _allPointageData = state.courseLogData;
              _filteredPointageData = _filterPointageData(_allPointageData, _searchQuery);
            });
          }
        },
        builder: (context, state) {
          if (state is CourseLogLoading) {
            return const Center(child: CustomSpinner());
          }

          return RefreshIndicator(
            onRefresh: _loadPointageData,
            child: CustomScrollView(
              slivers: [
                CustomAppBar(
                  pageSection: HeaderEnum.pointage,
                  title: "POINTAGES",
                  isSearchBarVisible: _isSearchBarVisible,
                  etablissementUtilisateur: widget.school,
                  enfantDuTuteur: widget.etudiant,
                  onSearchTap: _toggleSearchBarVisibility,
                ),
                AnimatedBuilder(
                  animation: _searchAnimationController,
                  builder: (context, child) =>
                  SliverPersistentHeader(
                    pinned: true,
                    delegate: SearchBarSliver(
                      extentHeight: _searchAnimationController.value * (_selectedYear != null ? 100.0 : 60.0),
                      searchController: _searchController,
                      onSearchChanged: (query) => setState(() {
                        _searchQuery = query;
                       _filteredPointageData = _filterPointageData(_allPointageData, _searchQuery);
                      }),
                      onDateFilterChanged: _onDateFilterChanged,
                      showYear: true,
                      hasActiveFilter: _selectedYear != null,
                      onClearDateFilter: _onClearDateFilter,
                      hintText: "Rechercher un cours...",
                      endDate: _selectedYear.toString(),
                    ),
                  ),
                ),
                _buildPointageList(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildPointageList() {
    if (_filteredPointageData.isEmpty) {
      return SliverFillRemaining(
        child: EmptyMessage(
          message: "Aucun pointage trouvé",
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          if (index < _filteredPointageData.length) {
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 7, vertical: 4),
              child: PointageDayGroup(dayLogs: _filteredPointageData[index]),
            );
          }
          return const SizedBox.shrink();
        },
        childCount: _filteredPointageData.length,
      ),
    );
  }
}
