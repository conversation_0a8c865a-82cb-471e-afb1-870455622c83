import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/usecases/get_course_log_usecase.dart';
import '../../domain/usecases/get_course_log_annuel_usecase.dart';
import '../../data/datasources/course_log_remote_datasource.dart';
import 'course_log_state.dart';
import 'package:kairos/core/constants/app_constants.dart';

/// CourseLog Cubit for managing course log state
class CourseLogCubit extends Cubit<CourseLogState> {
  final GetCourseLogUseCase getCourseLogUseCase;
  final GetCourseLogAnnuelUseCase getCourseLogAnnuelUseCase;
  final CourseLogRemoteDataSource remoteDataSource;

  CourseLogCubit({
    required this.getCourseLogUseCase,
    required this.getCourseLogAnnuelUseCase,
    required this.remoteDataSource,
  }) : super(CourseLogInitial());

  /// Load course log data
  Future<void> loadCourseLogData({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    try {
      emit(CourseLogLoading());

      debugPrint('CourseLogCubit - Loading course log data with params:');
      debugPrint('  codeEtab: $codeEtab');
      debugPrint('  telephone: $telephone');
      debugPrint('  codeEtudiant: $codeEtudiant');
      debugPrint('  codeUtilisateur: $codeUtilisateur');

      final result = await getCourseLogUseCase(GetCourseLogParams(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
      ));

      result.fold(
        (failure) {
          debugPrint('CourseLogCubit - Error loading course log data: ${failure.message}');
          emit(CourseLogError(message: failure.message));
        },
        (courseLogData) {
          debugPrint('CourseLogCubit - Successfully loaded course log data: ${courseLogData.length} dates');
          emit(CourseLogLoaded(courseLogData: courseLogData));
        },
      );
    } catch (e) {
      debugPrint('CourseLogCubit - ${AppConstants.unknownErrorMessage}: $e');
      emit(CourseLogError(message: 'Une erreur inattendue s\'est produite: $e'));
    }
  }

  /// Load filtered course log data (annual)
  Future<void> loadFilteredCourseLogData({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    required int annee,
  }) async {
    try {
      emit(CourseLogLoading());

      debugPrint('CourseLogCubit - Loading filtered course log data with params:');
      debugPrint('  codeEtab: $codeEtab');
      debugPrint('  telephone: $telephone');
      debugPrint('  codeEtudiant: $codeEtudiant');
      debugPrint('  codeUtilisateur: $codeUtilisateur');
      debugPrint('  annee: $annee');

      final result = await getCourseLogAnnuelUseCase(GetCourseLogAnnuelParams(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
        annee: annee,
      ));

      result.fold(
        (failure) {
          debugPrint('CourseLogCubit - Error loading filtered course log data: ${failure.message}');
          emit(CourseLogError(message: failure.message));
        },
        (courseLogData) {
          debugPrint('CourseLogCubit - Successfully loaded filtered course log data: ${courseLogData.length} dates');
          emit(CourseLogLoaded(courseLogData: courseLogData));
        },
      );
    } catch (e) {
      debugPrint('CourseLogCubit - ${AppConstants.unknownErrorMessage}: $e');
      emit(CourseLogError(message: 'Une erreur inattendue s\'est produite: $e'));
    }
  }

  /// Load mock pointage data for PRF users
  Future<void> loadMockPointageData() async {
    try {
      emit(CourseLogLoading());

      debugPrint('CourseLogCubit - Loading mock pointage data for PRF user');

      final mockData = await remoteDataSource.getMockPointageData();
      final entities = mockData.map((list) => list.map((model) => model.toEntity()).toList()).toList();

      debugPrint('CourseLogCubit - Successfully loaded mock pointage data: ${entities.length} dates');
      emit(CourseLogLoaded(courseLogData: entities));
    } catch (e) {
      debugPrint('CourseLogCubit - Error loading mock pointage data: $e');
      emit(CourseLogError(message: 'Une erreur inattendue s\'est produite: $e'));
    }
  }

  /// Reset to initial state
  void reset() {
    emit(CourseLogInitial());
  }
}