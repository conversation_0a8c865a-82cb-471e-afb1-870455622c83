

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:flutter_html/flutter_html.dart';
class HomeSlideWidget extends StatefulWidget{
  const HomeSlideWidget({super.key, required this.pageController});
  final PageController pageController;

  @override
  State<HomeSlideWidget> createState() => _HomeSlideWidgetState();
}

class _HomeSlideWidgetState extends State<HomeSlideWidget>{
  String? _htmlData;

  @override
  void initState(){
    super.initState();
    loadHtml();
  }


  Future<void> loadHtml() async{
    final data = await rootBundle.loadString("assets/documents/privacy_policy.html");
    setState(() {
      _htmlData = data;
    });
  }

  @override
  Widget build(BuildContext context){
    return Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Text("BIENVENUE SUR ", style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Theme.of(context).primaryColor)),
            SvgPicture.asset("assets/images/logo_kairos.svg"),
            SizedBox(height: 20, 
                    width: 200, 
                    child: Divider(color: Theme.of(context).primaryColor, thickness: 4,),),
            Spacer(),
            SvgPicture.asset("assets/images/bienvenue.svg"),
            Spacer(),
            SizedBox(height: 20),
            Spacer(),
            FilledButton(
              style: ButtonStyle(
                  backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
                  fixedSize: WidgetStateProperty.all(Size(300, 50))),
              onPressed: () {
                debugPrint('the user didAcceptPolicy AND clicked on `Continue` button');
                widget.pageController.nextPage(duration: Duration(milliseconds: 500), curve: Curves.easeInOut);
              },
              child: Text("CONTINUER", style: TextStyle(fontWeight: FontWeight.bold), ),
              ),
              SizedBox(height: 10,),
              Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              // child: Text("En cliquant sur `Continuer`, vous acceptez notre politique de confidentialité", 
              child: Text.rich(textAlign: TextAlign.center, TextSpan(
                children: [
                  TextSpan(text: "En cliquant sur "),
                  TextSpan(text: "CONTINUER", style: TextStyle(fontWeight: FontWeight.bold)),
                  TextSpan(text: ", vous acceptez notre"),
                  TextSpan(text: " politique de confidentialité", 
                          recognizer: TapGestureRecognizer()..onTap = () async {
                            showModalBottomSheet(context: context, builder: (context) => SizedBox(
                              height: 1200,
                              child: Padding(
                                padding: const EdgeInsets.all(20.0),
                                child: CustomScrollView(
                                  slivers: [
                                    SliverToBoxAdapter(
                                      child: Column(
                                    children: [
                                      SizedBox(height: 7),],
                                  ),),
                                  SliverToBoxAdapter(
                                    child: Column(
                                      children: [_htmlData != null ? Html(data: _htmlData): CircularProgressIndicator(),
                                      ],),)
                                  ]
                                ),
                              ),
                            ),);
                          },
                          style: TextStyle(color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold, decoration: TextDecoration.underline)),
                ]
              )),
              // textAlign: TextAlign.center, style: TextStyle(fontSize: 12)),
            ),
            Spacer(),
          
          ],
        );
  }
}