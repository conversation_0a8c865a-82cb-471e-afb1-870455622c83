import 'dart:convert';

import 'package:kairos/core/api/api_exception.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

import '../../../../core/api/api_client.dart';
import '../../../../core/api/api_endpoints.dart';
import '../../../../core/error/exceptions.dart';
import '../models/course_log_model.dart';

/// Remote data source interface for course log operations
abstract class CourseLogRemoteDataSource {
  /// Get course log data from API
  Future<List<List<CourseLogModel>>> getCourseLog({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });

  /// Get annual course log data with year filtering from API
  Future<List<List<CourseLogModel>>> getCourseLogAnnuel({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    required int annee,
  });

  /// Get mock pointage data for PRF users
  Future<List<List<CourseLogModel>>> getMockPointageData();
}

/// Implementation of CourseLogRemoteDataSource
class CourseLogRemoteDataSourceImpl implements CourseLogRemoteDataSource {
  final ApiClient apiClient;

  CourseLogRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<List<List<CourseLogModel>>> getCourseLog({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    try {
      // Build query parameters
      final Map<String, String> queryParams = {
        'codeEtab': codeEtab,
        'numeroTel': telephone.replaceAll("+",""),
        'codeEtudiant': codeEtudiant,
      };

      if (codeUtilisateur != null && codeUtilisateur.isNotEmpty) {
        queryParams['codeUtilisateur'] = codeUtilisateur;
      }

      debugPrint('CourseLogRemoteDataSource - getCourseLog called with params: $queryParams');

      final response = await apiClient.getWithToken(
        ApiEndpoints.cahierTexte,
        queryParameters: queryParams,
        options: Options(responseType: ResponseType.bytes)
      );

      final String responseString = latin1.decode(response.data);
      debugPrint('CourseLogRemoteDataSource - Raw response: $responseString');

      final jsonResponse = json.decode(responseString);
      debugPrint('CourseLogRemoteDataSource - Parsed JSON: $jsonResponse');

// Parse response as map of date -> list of items
      if (jsonResponse is Map<String, dynamic>) {
        final entries = jsonResponse.entries.toList()
          ..sort((a, b) => b.key.compareTo(a.key)); 
          debugPrint('sorted entries ----> $entries');
        return entries.map<List<CourseLogModel>>((entry) {
          final courseLog = entry.value;
          if (courseLog is List) {
            return courseLog.map<CourseLogModel>((item) => CourseLogModel.fromJson(item)).toList();
          }
          return <CourseLogModel>[];
        }).toList();
      }

      return [];
    } on DioException catch (e) {
      debugPrint('CourseLogRemoteDataSource: DioException: $e');
      debugPrint('CourseLogRemoteDataSource: DioException response: ${e.response}');
      debugPrint('CourseLogRemoteDataSource: DioException DAT -->: ${e.response?.data}');
      if (e.response != null && e.response!.data is Map<String, dynamic>) {
        // Handle API error response
        final apiException = ApiException.fromJson(
          e.response!.data,
          e.response!.statusCode
        );
        throw ServerException(apiException.getUserMessage());
      } else if (e.response != null && e.response!.data is List) {
        throw ServerException('Erreur de format de réponse inattendue lors de la récupération du cahier de texte. Veuillez réessayer plus tard.');
      }
      else {
        // Handle network error
        debugPrint('ActivateSchoolRemoteDatasourceImpl: NetworkException: ${e.message}');
        throw ServerException('Erreur de connexion lors de l\'activation de l\'école: ${e.message}');
      }
    } catch (e) {
      print('CourseLogRemoteDataSource - Error in getCourseLog: $e');
      throw ServerException('Failed to fetch course log data: $e');
    }
  }

  @override
  Future<List<List<CourseLogModel>>> getCourseLogAnnuel({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    required int annee,
  }) async {
    try {
      // Build query parameters
      final Map<String, String> queryParams = {
        'codeEtab': codeEtab,
        'numeroTel': telephone.replaceAll("+",""),
        'codeEtudiant': codeEtudiant,
      };

      if (codeUtilisateur != null && codeUtilisateur.isNotEmpty) {
        queryParams['codeUtilisateur'] = codeUtilisateur;
      }

      // Add year parameter for annual filtering
      queryParams['annee'] = annee.toString();

      print('CourseLogRemoteDataSource - getCourseLogAnnuel called with params: $queryParams');

      final response = await apiClient.getWithToken(
        ApiEndpoints.cahierTexteAnnuel,
        queryParameters: queryParams,
        options: Options(responseType: ResponseType.bytes)
      );


      final String responseString = latin1.decode(response.data);
      debugPrint('CourseLogRemoteDataSource - Raw response: $responseString');

      final jsonResponse = json.decode(responseString);
      print('CourseLogRemoteDataSource - Parsed JSON: $jsonResponse');

     // Parse response as map of date -> list of items
      if (jsonResponse is Map<String, dynamic>) {
        final entries = jsonResponse.entries.toList()
          ..sort((a, b) => a.key.compareTo(b.key)); // sort by date ascending
        return entries.map<List<CourseLogModel>>((entry) {
          final courseLog = entry.value;
          if (courseLog is List) {
            return courseLog.map<CourseLogModel>((item) => CourseLogModel.fromJson(item)).toList();
          }
          return <CourseLogModel>[];
        }).toList();
      }

      return [];
    } catch (e) {
      print('CourseLogRemoteDataSource - Error in getCourseLogAnnuel: $e');
      throw ServerException('Failed to fetch annual course log data: $e');
    }
  }

  @override
  Future<List<List<CourseLogModel>>> getMockPointageData() async {
    // Mock pointage data based on Figma design
    final mockData = [
      // Wednesday 18/04
      [
        CourseLogModel(
          dateCours: '2025-04-18',
          heureDebutPrevu: '07:30',
          heureFinPrevu: '10:00',
          heureDebutSaisi: '07:30',
          heureFinSaisi: '10:00',
          dureeSaisi: '2H30',
          cours: 'HTML/CSS',
          professeur: 'MALAMINE AVAGTEAZEA',
          semestre: 'Semestre 1',
          auteurSaisi: 'MALAMINE AVAGTEAZEA',
          etudiant: '',
          classe: 'LPTI | Licence 3 | LPTI 1A',
          contenuSaisi: 'Pointage validé',
          responsableUsername: 'P20001',
          idObject: 1,
          matriculeProfesseur: 'P20001',
          idPieceJointe: 0,
        ),
      ],
      // Tuesday 17/04
      [
        CourseLogModel(
          dateCours: '2025-04-17',
          heureDebutPrevu: '09:00',
          heureFinPrevu: '11:00',
          heureDebutSaisi: '09:00',
          heureFinSaisi: '11:00',
          dureeSaisi: '2H',
          cours: 'PHP',
          professeur: 'MALAMINE AVAGTEAZEA',
          semestre: 'Semestre 1',
          auteurSaisi: 'MALAMINE AVAGTEAZEA',
          etudiant: '',
          classe: 'LPTI | Licence 3 | LPTI 1A',
          contenuSaisi: 'Pointage validé',
          responsableUsername: 'P20001',
          idObject: 2,
          matriculeProfesseur: 'P20001',
          idPieceJointe: 0,
        ),
        CourseLogModel(
          dateCours: '2025-04-17',
          heureDebutPrevu: '13:00',
          heureFinPrevu: '15:00',
          heureDebutSaisi: '13:00',
          heureFinSaisi: '15:00',
          dureeSaisi: '2H',
          cours: 'ETHICAL HACKING',
          professeur: 'MALAMINE AVAGTEAZEA',
          semestre: 'Semestre 1',
          auteurSaisi: 'MALAMINE AVAGTEAZEA',
          etudiant: '',
          classe: 'LPTI | Licence 3 | LPTI 1A',
          contenuSaisi: 'Pointage décompté',
          responsableUsername: 'P20001',
          idObject: 3,
          matriculeProfesseur: 'P20001',
          idPieceJointe: 0,
        ),
      ],
      // Monday 16/04
      [
        CourseLogModel(
          dateCours: '2025-04-16',
          heureDebutPrevu: '10:30',
          heureFinPrevu: '12:30',
          heureDebutSaisi: '10:30',
          heureFinSaisi: '12:30',
          dureeSaisi: '2H',
          cours: 'AWS',
          professeur: 'MALAMINE AVAGTEAZEA',
          semestre: 'Semestre 1',
          auteurSaisi: 'MALAMINE AVAGTEAZEA',
          etudiant: '',
          classe: 'LPTI | Licence 3 | LPTI 1A',
          contenuSaisi: 'Pointage décompté',
          responsableUsername: 'P20001',
          idObject: 4,
          matriculeProfesseur: 'P20001',
          idPieceJointe: 0,
        ),
      ],
    ];

    return mockData;
  }
}