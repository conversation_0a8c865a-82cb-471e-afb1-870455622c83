﻿import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/error/failures.dart';
import '../../domain/usecases/load_dashboard_data_usecase.dart';
import 'dashboard_state.dart';
import '../../../../core/error/exceptions.dart';
import 'package:kairos/core/constants/app_constants.dart';

/// Dashboard Cubit for managing dashboard state
class DashboardCubit extends Cubit<DashboardState> {
  /// Use case for loading dashboard data.
  final LoadDashboardDataUseCase loadDashboardDataUseCase;

  /// Constructor for DashboardCubit.
  DashboardCubit(this.loadDashboardDataUseCase) : super(const DashboardInitial());

  /// Load dashboard data with parameters
  Future<void> loadDashboardData({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    emit(const DashboardLoading()); // Indicate loading state

    final params = LoadDashboardDataParams(
      codeEtab: codeEtab,
      telephone: telephone,
      codeEtudiant: codeEtudiant,
      codeUtilisateur: codeUtilisateur,
    );

    final failureOrDashboardData = await loadDashboardDataUseCase(params);
    debugPrint('DASHBOARD CUBIT --> DashboardEntity: $failureOrDashboardData');

    failureOrDashboardData.fold(
      (failure) {
        // Handle failure and emit error state
        emit(DashboardError(_mapFailureToMessage(failure)));
      },
      (dashboardData) {
        // Emit success state with dashboard data
        debugPrint('DASHBOARD CUBIT --> DashboardLoaded State: $dashboardData');
        emit(DashboardLoaded(dashboardData: dashboardData));
      },
    );
  }

  /// Refresh dashboard data
  Future<void> refresh({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    await loadDashboardData(
      codeEtab: codeEtab,
      telephone: telephone,
      codeEtudiant: codeEtudiant,
      codeUtilisateur: codeUtilisateur,
    );
  }

  /// Map Failure to a user-friendly message
  String _mapFailureToMessage(Failure failure) {
    debugPrint('Failure type: ${failure.runtimeType}');
    if (failure is ServerFailure) {
      debugPrint('ServerFailure: ${failure.message}');
      debugPrint('Exception -->: ${failure.exception}');
      if (failure.exception is ServerException) {
        return (failure.exception as ServerException).message;
      }
      return failure.message;
    } else if (failure is AuthenticationFailure) {
      return failure.message;
    } else if (failure is NetworkFailure) {
      return failure.message;
    } else {
      return AppConstants.unknownErrorMessage;
    }
  }

  void setDashboardLoaded() {
    debugPrint('Setting dashboard loaded');
    emit(DashboardLoaded(dashboardData: null));
  }

  /// Set dashboard loaded with specific data (for mock data scenarios)
  void setDashboardLoadedWithData(dynamic dashboardData) {
    debugPrint('Setting dashboard loaded with data: $dashboardData');
    emit(DashboardLoaded(dashboardData: dashboardData));
  }
}
