import 'package:kairos/features/schools/presentation/bloc/financial_status_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kairos/core/widgets/layout/custom_app_bar.widget.dart';
import 'package:kairos/core/widgets/inputs/search_bar_sliver.widget.dart';
import 'package:kairos/core/enums/header_enums.dart';
import 'package:kairos/features/schools/presentation/bloc/financial_status_state.dart'; // Import FinancialStatusState
import 'package:kairos/features/schools/presentation/pages/liste_etablissements/liste_etablissement_widgets/alert.widget.dart';
import 'package:kairos/features/student_records/presentation/bloc/student_records_cubit.dart';
import 'package:kairos/features/student_records/presentation/bloc/student_records_state.dart';
import 'package:kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart';
import 'package:kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:kairos/core/di/injection_container.dart';
import 'dart:convert';
import 'package:kairos/core/utils/navigation_utils.dart';

class DossierSelectionPage extends StatefulWidget {
  const DossierSelectionPage({super.key});

  @override
  State<DossierSelectionPage> createState() => _DossierSelectionPageState();
}

class _DossierSelectionPageState extends State<DossierSelectionPage> with TickerProviderStateMixin {
  bool _isSearchBarVisible = false;
  final TextEditingController _searchController = TextEditingController();
  late List<EnfantTuteurEntity> filteredStudents = [];
  late AnimationController _searchAnimationController;
  final AuthLocalDataSource _authLocalDataSource = sl<AuthLocalDataSource>();
  EtablissementUtilisateur? _currentSchool;

  @override
  void initState() {
    super.initState();
    filteredStudents = [];
    _searchController.addListener(_filterStudents);
    _searchAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _loadSchoolDataAndChildren();
  }

  /// Load school data from route arguments and fetch children
  void _loadSchoolDataAndChildren() {
    // Get school data from route arguments
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final args = ModalRoute.of(context)?.settings.arguments;

    debugPrint("DOSSIER_SELECTION: Loading school data and children... ${args}");
      if (args is EtablissementUtilisateur) {
        setState(() {
          _currentSchool = args;
        });
        _loadChildren();
      }
    });
  }

  /// Load children data using BLoC
  Future<void> _loadChildren() async {
    if (_currentSchool == null) return;

    try {
      final phoneNumber = await _authLocalDataSource.getPhoneNumber();
      if (phoneNumber != null && context.mounted) {
        context.read<StudentRecordsCubit>().loadEnfantsDuTuteur(
          codeUtilisateur: _currentSchool!.codeUtilisateur,
          codeEtab: _currentSchool!.codeEtab,
          telephone: phoneNumber,
        );
      }
    } catch (e) {
      debugPrint('Error loading children: $e');
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchAnimationController.dispose();
    super.dispose();
  }

  void _filterStudents() {
    // This will be called when BLoC state changes, so we need to get the current children list
    final currentState = context.read<StudentRecordsCubit>().state;
    if (currentState is StudentRecordsLoaded) {
      final String query = _searchController.text.toLowerCase();
      setState(() {
        filteredStudents = currentState.children.where((student) {
          final fullName = '${student.prenom} ${student.nom}';
          return fullName.toLowerCase().contains(query) ||
                 student.codeEtudiant.toLowerCase().contains(query) ||
                 student.classeInscrite.toLowerCase().contains(query);
        }).toList();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: MultiBlocListener(
        listeners: [
          BlocListener<StudentRecordsCubit, StudentRecordsState>(
            listener: (context, state) {
              if (state is StudentRecordsLoaded) {
                setState(() {
                  filteredStudents = state.children;
                });
              }
            },
          ),
          BlocListener<FinancialStatusCubit, FinancialStatusState>(
            listener: (context, state) {
              if (state is FinancialStatusSuccess) {
                // Find the student associated with this financial status update
                final student = filteredStudents.firstWhere(
                  (s) => s.codeEtudiant == state.financialStatus.username,
                  orElse: () => throw Exception("Student not found for financial status update"),
                );

                if (!state.financialStatus.enRegle) {
                  showDialog(
                    context: context,
                    builder: (context) => AlertWidget(
                      message: "Vous avez des frais impayés. Veuillez régulariser votre situation afin d'accèder á votre espace",
                      school: _currentSchool!,
                      student: student,
                    ),
                  );
                } else {
                  debugPrint('current school-user: $_currentSchool');
                  NavigationUtils.pushNamed(
                    context,
                    '/dashboard',
                    arguments: {
                      'etudiant': student,
                      'school': _currentSchool
                    },
                  );
                }
              } else if (state is FinancialStatusError) {
                // Show snackbar on error
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text("Erreur: ${state.message}")),
                );
              }
            },
          ),
        ],
        child: CustomScrollView(
          slivers: [
            CustomAppBar(
              pageSection: HeaderEnum.dossiers,
              title: "COMPTES ÉTUDIANTS",
              isSearchBarVisible: _isSearchBarVisible,
              etablissementUtilisateur: _currentSchool,
              onSearchTap: () {
                setState(() {
                  _isSearchBarVisible = !_isSearchBarVisible;
                  if (!_isSearchBarVisible) {
                    _searchAnimationController.reverse();
                    _searchController.clear();
                  } else {
                    _searchAnimationController.forward();
                  }
                });
              },
            ),
            SliverToBoxAdapter(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                    child: Text(
                      "À quel dossier souhaitez-vous accéder ?".toUpperCase(),
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Divider(color: Theme.of(context).primaryColor, thickness: 4, height: 20, indent: 100, endIndent: 100,),
                ],
              ),
            ),
          AnimatedBuilder(
              animation: _searchAnimationController,
              builder: (context, child) {
                return SliverPersistentHeader(
                  pinned: true,
                  delegate: SearchBarSliver(
                    extentHeight: _searchAnimationController.value * 40.0,
                    searchController: _searchController,
                    onSearchChanged: (query) => _filterStudents(),
                    hintText: "Rechercher un dossier...",
                    showDateFilter: false,
                  ),
                );
              },
            ),
            BlocBuilder<StudentRecordsCubit, StudentRecordsState>(
              builder: (context, state) {
                if (state is StudentRecordsLoading) {
                  return const SliverFillRemaining(
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  );
                } else if (state is StudentRecordsError) {
                  return SliverFillRemaining(
                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.all(40.0),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.error_outline,
                              size: 64,
                              color: Colors.red[300],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'Erreur de chargement',
                              style: Theme.of(context).textTheme.headlineSmall,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              state.message,
                              textAlign: TextAlign.center,
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: _loadChildren,
                              child: const Text('Réessayer'),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                } else if (state is StudentRecordsLoaded) {
                  if (filteredStudents.isEmpty) {
                    return const SliverFillRemaining(
                      child: Center(
                        child: Text(
                          'Aucun étudiant trouvé',
                          style: TextStyle(fontSize: 16),
                        ),
                      ),
                    );
                  }

                  return SliverPadding(
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    sliver: SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                          final student = filteredStudents[index];
                          return Card(
                            shape: RoundedRectangleBorder(
                              side: BorderSide(color: Theme.of(context).colorScheme.secondary, width: 1),
                              borderRadius: BorderRadius.circular(1.0),
                            ),
                            color: Colors.white,
                            shadowColor: Theme.of(context).colorScheme.secondary,
                            margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                            child: ListTile(
                              leading: CircleAvatar(
                                radius: 20,
                                backgroundColor: Colors.grey[300],
                                backgroundImage: student.photo.isNotEmpty && student.photo != "null"
                                    ? MemoryImage(base64Decode(student.photo))
                                    : null,
                                child: student.photo.isEmpty
                                    ? const Icon(Icons.person, color: Colors.grey)
                                    : null,
                              ),
                              title: Text.rich(
                                TextSpan(children: [
                                  TextSpan(text: student.codeEtudiant, style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
                                  const TextSpan(text: " - ", style: TextStyle(fontSize: 12)),
                                  TextSpan(text: student.prenom, style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
                                  const TextSpan(text: " ", style: TextStyle(fontSize: 12)),
                                  TextSpan(text: student.nom, style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
                                ]),
                              ),
                              subtitle: Text(student.classeInscrite, style: const TextStyle(fontSize: 11)),
                              trailing: BlocBuilder<FinancialStatusCubit, FinancialStatusState>(
                                builder: (context, state) {
                                  if (state is FinancialStatusLoading && state.codeEtudiant == student.codeEtudiant) {
                                    return const SizedBox(
                                      width: 16, // Slightly larger size for spinner
                                      height: 16,
                                      child: CircularProgressIndicator(strokeWidth: 2),
                                    );
                                  } else {
                                    return const Icon(Icons.keyboard_arrow_right_rounded);
                                  }
                                },
                              ),
                              onTap: () async {
                                // Check if the cubit is currently loading
                                final currentState = context.read<FinancialStatusCubit>().state;
                                if (currentState is! FinancialStatusLoading) {
                                  final numeroTelephone = await _authLocalDataSource.getPhoneNumber();
                                  if (numeroTelephone != null && context.mounted) {
                                    context.read<FinancialStatusCubit>().checkFinancialStatus(
                                      codeEtab: _currentSchool!.codeEtab,
                                      codeEtudiant: student.codeEtudiant,
                                      codeUtilisateur: _currentSchool!.codeUtilisateur,
                                      telephone: numeroTelephone,
                                    );
                                  }
                                }
                              },
                            ),
                          );
                        },
                        childCount: filteredStudents.length,
                      ),
                    ),
                  );
                } else {
                  return const SliverFillRemaining(
                    child: Center(
                      child: Text('Chargement...'),
                    ),
                  );
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
