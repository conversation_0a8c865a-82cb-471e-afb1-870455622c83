import 'package:flutter/material.dart';
import 'package:kairos/features/course_log/domain/entities/course_log_entity.dart';
import 'pointage_item.widget.dart';
import 'package:kairos/core/utils/date_utils.dart' as du;

/// Widget that groups pointage entries by day
class PointageDayGroup extends StatelessWidget {
  final List<CourseLogEntity> dayLogs;

  const PointageDayGroup({
    super.key,
    required this.dayLogs,
  });

  @override
  Widget build(BuildContext context) {
    if (dayLogs.isEmpty) return const SizedBox.shrink();

    // Get the first entry to extract date information
    final firstEntry = dayLogs.first;
    final date = du.parseDate(firstEntry.dateCours);
    
    // Format day name and date
    final dayName = _getDayName(date.weekday);
    final formattedDate = "${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}";

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Day header
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                dayName,
                style: const TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w700,
                  fontSize: 21,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                formattedDate,
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w600,
                  fontSize: 12,
                  color: Colors.black.withOpacity(0.8),
                ),
              ),
            ],
          ),
        ),
        
        // Pointage items
        ...dayLogs.map((pointage) => PointageItem(pointage: pointage)),
        
        const SizedBox(height: 16),
      ],
    );
  }

  String _getDayName(int weekday) {
    switch (weekday) {
      case 1:
        return 'LUN';
      case 2:
        return 'MAR';
      case 3:
        return 'MER';
      case 4:
        return 'JEU';
      case 5:
        return 'VEN';
      case 6:
        return 'SAM';
      case 7:
        return 'DIM';
      default:
        return '';
    }
  }
}
