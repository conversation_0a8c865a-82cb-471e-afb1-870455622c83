import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import '../../../../core/api/api_client.dart';
import '../../../../core/api/api_endpoints.dart';
import '../../../../core/api/api_exception.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/services/device_info_service.dart';
import '../models/send_sms_request.dart';
import '../models/sms_response_model.dart'; // Import the new model
import '../models/password_reset_model.dart';
import '../models/reset_password_result.dart'; // Import the new ResetPasswordResult
import '../../domain/entities/sms_response_entity.dart'; // Import the new entity
import '../../domain/entities/password_reset_entity.dart';

/// Abstract interface for authentication remote data source
abstract class AuthRemoteDataSource {

  /// Send SMS for verification
  Future<SmsResponseEntity> sendSms(String phoneNumber); // Update return type
  
  /// Refresh authentication token
  Future<String> refreshToken();

  /// Resend SMS for verification
  Future<dynamic> resendSms(String phoneNumber);

  /// Verify PIN code with full name and device info
  Future<dynamic> verifyPinWithDetails(String fullName, String otp, Map<String, dynamic> deviceInfo, String phoneNumber, {
    bool tokenExists = false,
  });

  /// Check the user's response to the reactivation dialog
  Future<dynamic> checkResponse(String phoneNumber, {required bool activated});

  /// Send password reset email
  Future<PasswordResetResponseEntity> sendPasswordResetEmail(String email, String codeEtab);

  /// Reset password with PIN and new password
  Future<ResetPasswordResult> resetPassword(
    String codeEtab,
    String email,
    String pin,
    String newPassword,
    String confirmPassword,
  );
}

/// Implementation of AuthRemoteDataSource
class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final ApiClient apiClient;

  AuthRemoteDataSourceImpl({required this.apiClient});


  // =========================================== SEND SMS ================================================
  @override
  Future<SmsResponseEntity> sendSms(String phoneNumber) async { // Update return type
    try {
      // Get device info from service
      final deviceInfo = DeviceInfoService.deviceInfo;

      // Create request payload
      final sendSmsRequest = SendSmsRequest(
        numeroTelephone: phoneNumber,
        marqueTelephone: deviceInfo.marqueTelephone,
        modelTelephone: deviceInfo.modelTelephone,
        imeiTelephone: deviceInfo.imeiTelephone,
        numeroSerie: deviceInfo.numeroSerie,
      );

      // Make API call
      final response = await apiClient.post(
        ApiEndpoints.sendSms,
        data: sendSmsRequest.toJson(),
      );
      
      debugPrint('AuthRemoteDataSourceImpl: send sms response: ${response.data}');

      // Check response status
      if (response.statusCode != 200 && response.statusCode != 201) {
        throw ServerException('Failed to send SMS: ${response.statusMessage}');
      }

      // Parse the response data into SmsResponseModel and return it
      return SmsResponseModel.fromJson(response.data);

    } on DioException catch (e) {
      if (e.response != null) {
        // Handle API error response
        final apiException = ApiException.fromJson(
          e.response!.data,
          e.response!.statusCode,
        );
        throw ServerException(apiException.getUserMessage());
      } else {
        // Handle network error
        throw NetworkException('Erreur de connexion: ${e.message?? ''}');
      }
    } catch (e) {
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }
      throw ServerException('Erreur inattendue lors de l\'envoi du SMS: $e');
    }
  }
  

  // =========================================== REFRESH TOKEN ================================================
  @override
  Future<String> refreshToken() async {
    // TODO: Implement API call
    throw UnimplementedError('Refresh token API call not implemented yet');
  }
  


  // =========================================== RESEND SMS ================================================
  @override
  Future<dynamic> resendSms(String phoneNumber) async {
    try {
      // Get device info from service
      final deviceInfo = DeviceInfoService.deviceInfo;

      // Create request payload
      final sendSmsRequest = SendSmsRequest(
        numeroTelephone: phoneNumber,
        marqueTelephone: deviceInfo.marqueTelephone,
        modelTelephone: deviceInfo.modelTelephone,
        imeiTelephone: deviceInfo.imeiTelephone,
        numeroSerie: deviceInfo.numeroSerie,
      );

      // Make API call
      final response = await apiClient.post(
        ApiEndpoints.resendSms, // Use the resend SMS endpoint
        data: sendSmsRequest.toJson(),
      );

      debugPrint('AuthRemoteDataSourceImpl: resend sms response: ${response.data}');

      // Check response status
      if (response.statusCode != 200 && response.statusCode != 201) {
        throw ServerException('Failed to resend SMS: ${response.statusMessage}');
      }

      return response.data;

      // Optionally parse response if needed
      // final sendSmsResponse = SendSmsResponse.fromJson(response.data);

    } on DioException catch (e) {
      if (e.response != null) {
        // Handle API error response
        final apiException = ApiException.fromJson(
          e.response!.data,
          e.response!.statusCode,
        );
        throw ServerException(apiException.getUserMessage());
      } else {
        // Handle network error
        throw NetworkException('Erreur de connexion: ${e.message ?? ''}');
      }
    } catch (e) {
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }
      throw ServerException('Erreur inattendue lors du renvoi du SMS: $e');
    }
  }


  // =========================================== VERIFY PIN ================================================
  @override
  Future<dynamic> verifyPinWithDetails(
    String fullName,
    String otp,
    Map<String, dynamic> deviceInfo,
    String phoneNumber, {
    bool tokenExists = false,
  }) async {
    try {
      // Create request payload and endpoint based on tokenExists
      final String endpoint = tokenExists
          ? ApiEndpoints.refreshToken
          : ApiEndpoints.verifyPin;
      final Map<String, dynamic> requestPayload = tokenExists
          ? {
              'codeOtp': otp,
              'marqueTelephone': deviceInfo['marqueTelephone'],
              'modelTelephone': deviceInfo['modelTelephone'],
              'imeiTelephone': deviceInfo['imeiTelephone'],
              'numeroSerie': deviceInfo['numeroSerie'],
              'numeroTelephone': phoneNumber,
            }
          : {
              'nomComplet': fullName,
              'pin': otp,
              'marqueTelephone': deviceInfo['marqueTelephone'],
              'modelTelephone': deviceInfo['modelTelephone'],
              'imeiTelephone': deviceInfo['imeiTelephone'],
              'numeroSerie': deviceInfo['numeroSerie'],
              'numeroTelephone': phoneNumber,
            };

      // Make API call
      final response = await apiClient.post(
        endpoint, // Use the correct endpoint
        data: requestPayload,
      );

      debugPrint('AuthRemoteDataSourceImpl: verify pin response: \\${response.data}');

      // Check response status
      if (response.statusCode != 200 && response.statusCode != 201) {
        // Handle API error response with returnCode and userMessage
        if (response.data is Map<String, dynamic> && response.data.containsKey('returnCode') && response.data.containsKey('userMessage')) {
           throw ServerException(response.data['userMessage'], response.data['returnCode']);
        }
        throw ServerException('Failed to verify PIN: \\${response.statusMessage}');
      }

      return response.data;

    } on DioException catch (e) {
      debugPrint('AuthRemoteDataSourceImpl: verify pin DioException: \\$e');
      if (e.response != null) {
        // Handle API error response with returnCode and userMessage
        if (e.response!.data is Map<String, dynamic> && e.response!.data.containsKey('returnCode') && e.response!.data.containsKey('userMessage')) {
           throw ServerException(e.response!.data['userMessage'],e.response?.statusCode.toString(), e.response!.data['returnCode']);
        }
        // Handle generic API error response
        final apiException = ApiException.fromJson(
          e.response!.data,
          e.response!.statusCode,
        );
        throw ServerException(apiException.getUserMessage());
      } else {
        // Handle network error
        throw NetworkException('Erreur de connexion: ${e.message ?? ''}');
      }
    } catch (e) {
      debugPrint('AuthRemoteDataSourceImpl: verify pin catch: $e');
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }
      throw ServerException('Erreur inattendue lors de la vérification du PIN: $e');
    }
  }


  // =========================================== CHECK RESPONSE ================================================
  @override
  Future<dynamic> checkResponse(String phoneNumber, {required bool activated}) async {
    try {
      // Get device info from service
      final deviceInfo = DeviceInfoService.deviceInfo;

      // Create request payload including device info and activated status
      final requestPayload = {
        'numeroTelephone': phoneNumber,
        'marqueTelephone': deviceInfo.marqueTelephone,
        'modelTelephone': deviceInfo.modelTelephone,
        'imeiTelephone': deviceInfo.imeiTelephone,
        'numeroSerie': deviceInfo.numeroSerie,
        'activated': activated, // Add the activated boolean
      };

      // Make API call
      final response = await apiClient.post(
        ApiEndpoints.checkResponse, // Use the check response endpoint
        data: requestPayload,
      );

      debugPrint('AuthRemoteDataSourceImpl: check response: ${response.data}');

      // Check response status
      if (response.statusCode != 200 && response.statusCode != 201) {
        // Handle API error response with returnCode and userMessage
        if (response.data is Map<String, dynamic> && response.data.containsKey('returnCode') && response.data.containsKey('userMessage')) {
           throw ServerException(response.data['userMessage'], response.data['returnCode']);
        }
        throw ServerException('Failed to check response: ${response.statusMessage}');
      }

      return response.data;

    } on DioException catch (e) {
      if (e.response != null) {
        // Handle API error response with returnCode and userMessage
        if (e.response!.data is Map<String, dynamic> && e.response!.data.containsKey('returnCode') && e.response!.data.containsKey('userMessage')) {
           throw ServerException(e.response!.data['userMessage'], e.response!.data['returnCode']);
        }
        // Handle generic API error response
        final apiException = ApiException.fromJson(
          e.response!.data,
          e.response!.statusCode,
        );
        throw ServerException(apiException.getUserMessage());
      } else {
        // Handle network error
        throw NetworkException('Erreur de connexion: ${e.message?? ''}');
      }
    } catch (e) {
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }
      throw ServerException('Erreur inattendue lors de la vérification de la réponse: $e');
    }
  }



// =========================================== SEND PASSWORD RESET EMAIL ================================================
  @override
  Future<PasswordResetResponseEntity> sendPasswordResetEmail(String email, String codeEtab) async {
    try {
      // Create request payload
      final Map<String, dynamic> requestPayload = {
        'email': email,
        'codeEtab': codeEtab,
      };

      // Make API call
      final response = await apiClient.postWithToken(
        ApiEndpoints.forgotPassword, // We'll add this endpoint
        data: requestPayload,
      );

      // Parse response
      final responseModel = PasswordResetResponseModel.fromJson(response.data);

      
      // Check the status property of PasswordResponseEntity
      if (responseModel.status == false) {
        throw NotFoundException(
            "L'adresse e-mail fournie n'est pas associée à l'établissement sélectionné. Veuillez fournir une adresse e-mail et un établissement valides.");
      }
      
      return responseModel.toEntity();

    } on DioException catch (e) {
      if (e.response != null) {
        // Handle API error response
        final apiException = ApiException.fromJson(
          e.response!.data,
          e.response!.statusCode,
        );
        throw ServerException(apiException.getUserMessage());
      } else {
        // Handle network error
        throw NetworkException('Erreur de connexion: ${e.message}');
      }
    } catch (e) {
      if (e is ServerException || e is NetworkException || e is NotFoundException) {
        rethrow;
      }
      throw ServerException('Erreur inattendue lors de l\'envoi de l\'email: $e');
    }
  }




// =========================================== RESET PASSWORD ================================================
  @override
  Future<ResetPasswordResult> resetPassword(
    String codeEtab,
    String email,
    String pin,
    String newPassword,
    String confirmPassword,
  ) async {
    try {
      // Create request payload
      final Map<String, dynamic> requestPayload = {
        'codeEtab': codeEtab,
        'email': email,
        'pinCode': pin,
        'newPassword': newPassword,
        'confirmPassword': confirmPassword,
      };

      // Make API call
      final response = await apiClient.post(
        ApiEndpoints.resetPassword, // We'll add this endpoint
        data: requestPayload,
      );

      // Parse response based on success or error payload structure
      if (response.data is Map<String, dynamic>) {
        final Map<String, dynamic> responseData = response.data;

        if (responseData.containsKey('returnCode') && responseData['returnCode'] == 'SUCCESS') {
          // Success payload
          return ResetPasswordSuccess(responseData['reponse'] as String);
        } else if (responseData.containsKey('errorCode')) {
          // Validation Error payload
          throw ValidationException(responseData['debugMessage'] as String);
        }
      }
      // Handle unexpected response format
      throw ServerException('Format de réponse inattendu lors de la réinitialisation du mot de passe. Veuillez réessayer plus tard.');

    } on DioException catch (e) {
      if (e.response != null) {
        // Handle API error response
        final apiException = ApiException.fromJson(
          e.response!.data,
          e.response!.statusCode,
        );
        throw ServerException(apiException.getUserMessage());
      } else {
        // Handle network error
        throw NetworkException('Erreur de connexion: ${e.message}');
      }
    } catch (e) {
      if (e is ServerException || e is NetworkException || e is ValidationException) {
        rethrow;
      }
      throw ServerException('Erreur inattendue lors de la réinitialisation: $e');
    }
  }
}
