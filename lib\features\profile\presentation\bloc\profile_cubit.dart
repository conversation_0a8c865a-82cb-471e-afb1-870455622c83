﻿import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/di/injection_container.dart' as di;
import '../../domain/usecases/get_profile_usecase.dart';
import '../../domain/usecases/logout_usecase.dart';
import '../../domain/usecases/delete_account_usecase.dart'; // New import
import '../../domain/usecases/get_carte_virtuelle_usecase.dart'; // New import for virtual card use case
import '../../domain/usecases/update_profile_picture_usecase.dart'; // New import for profile picture upload
import '../../../authentication/data/models/deconnexion_request.dart';
import '../../../authentication/data/models/delete_account_request.dart'; // New import
import 'profile_state.dart';

/// Profile Cubit for managing profile state
class ProfileCubit extends Cubit<ProfileState> {
  final GetProfileUseCase _getProfileUseCase;
  final LogoutUseCase _logoutUseCase;
  final DeleteAccountUseCase _deleteAccountUseCase; // New use case
  final GetCarteVirtuelleUseCase _getCarteVirtuelleUseCase; // New use case for virtual card
  final UpdateProfilePictureUseCase _updateProfilePictureUseCase; // New use case for profile picture upload

  ProfileCubit({
    required GetProfileUseCase getProfileUseCase,
    required LogoutUseCase logoutUseCase,
    required DeleteAccountUseCase deleteAccountUseCase, // New dependency
    required GetCarteVirtuelleUseCase getCarteVirtuelleUseCase, // New dependency for virtual card
    required UpdateProfilePictureUseCase updateProfilePictureUseCase, // New dependency for profile picture upload
  }) : _getProfileUseCase = getProfileUseCase,
       _logoutUseCase = logoutUseCase,
       _deleteAccountUseCase = deleteAccountUseCase, // Initialize new use case
       _getCarteVirtuelleUseCase = getCarteVirtuelleUseCase, // Initialize new use case
       _updateProfilePictureUseCase = updateProfilePictureUseCase, // Initialize new use case
       super(const ProfileInitial());
  
  /// Load profile data for a specific user using codeUtilisateur
  Future<void> loadProfileData(String codeUtilisateur) async { // Accept codeUtilisateur
    emit(const ProfileLoading());

    // Pass the codeUtilisateur to the use case
    final failureOrProfile = await _getProfileUseCase(codeUtilisateur);

    failureOrProfile.fold(
      (failure) => emit(ProfileError(_mapFailureToMessage(failure))),
      (profile) {
        if (profile != null) {
          emit(ProfileLoaded(profile: profile));
        } else {
          emit(const ProfileNotFound());
        }
      },
    );
  }
  
  /// Refresh profile data for a specific user using codeUtilisateur
  Future<void> refresh(String codeUtilisateur) async { // Accept codeUtilisateur
    // Pass the codeUtilisateur to loadProfileData
    await loadProfileData(codeUtilisateur);
  }

  /// Logout user
  Future<void> logout(DeconnexionRequest request) async {
    emit(const LogoutLoading());

    final failureOrSuccess = await _logoutUseCase(request);

    failureOrSuccess.fold(
      (failure) => emit(ProfileError(_mapFailureToMessage(failure))),
      (_) => emit(const LogoutSuccess()),
    );
  }

  /// Delete user account
  Future<void> deleteAccount(DeleteAccountRequest request) async {
    emit(const ProfileDeleting());

    final failureOrSuccess = await _deleteAccountUseCase(request);

    failureOrSuccess.fold(
      (failure) => emit(ProfileError(_mapFailureToMessage(failure))),
      (_) => emit(const ProfileDeleted()),
    );
  }

  /// Get virtual card data
  Future<void> getCarteVirtuelle({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    emit(const CarteVirtuelleLoading());

    final failureOrCarteVirtuelle = await _getCarteVirtuelleUseCase(
      GetCarteVirtuelleParams(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
      ),
    );

    failureOrCarteVirtuelle.fold(
      (failure) => emit(CarteVirtuelleError(_mapFailureToMessage(failure))),
      (carteVirtuelle) => emit(CarteVirtuelleLoaded(carteVirtuelle: carteVirtuelle)),
    );
  }

  /// Update user profile picture
  Future<void> updateProfilePicture(String profilePicture) async {
    emit(const ProfilePictureUploading());

    try {
      // Get SharedPreferences instance
      final prefs = di.sl<SharedPreferences>();

      // Retrieve user information from SharedPreferences
      final String? phoneNumber = prefs.getString('numeroTelephone');
      if (phoneNumber == null) {
        emit(const ProfilePictureUploadError('Numéro de téléphone non disponible'));
        return;
      }

      // Get current profile state to extract user information
      final currentState = state;
      if (currentState is! ProfileLoaded) {
        emit(const ProfilePictureUploadError('Profil utilisateur non chargé'));
        return;
      }

      final profile = currentState.profile;

      // Create parameters for the use case
      final params = UpdateProfilePictureParams(
        codeEtab: profile.schoolCode,
        telephone: phoneNumber,
        codeUtilisateur: profile.codeUtilisateur.isNotEmpty ? profile.codeUtilisateur : null,
        codeEtudiant: profile.id, // Using profile.id as codeEtudiant
        profilePicture: profilePicture,
      );

      // Call the use case
      final failureOrSuccess = await _updateProfilePictureUseCase(params);

      failureOrSuccess.fold(
        (failure) => emit(ProfilePictureUploadError(_mapFailureToMessage(failure))),
        (_) => emit(const ProfilePictureUploadSuccess()),
      );

    } catch (e) {
      emit(ProfilePictureUploadError('Erreur inattendue: $e'));
    }
  }

  /// Map failure to user-friendly message
  String _mapFailureToMessage(Failure failure) {
    if (failure is ServerFailure) {
      return failure.message;
    } else if (failure is NetworkFailure) {
      return 'Erreur de connexion. Vérifiez votre connexion internet.';
    } else if (failure is CacheFailure) {
      return 'Erreur de stockage local.';
    } else {
      return 'Une erreur inattendue s\'est produite.';
    }
  }
}
