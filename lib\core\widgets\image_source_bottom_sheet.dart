import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:kairos/core/theme/color_schemes.dart';

class ImageSourceBottomSheet extends StatelessWidget {
  final Function(ImageSource) onSourceSelected;
  final VoidCallback onCancel;

  const ImageSourceBottomSheet({
    super.key,
    required this.onSourceSelected,
    required this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const Text(
            'Sélectionner une source',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: AppColorSchemes.primaryBlue,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 10),
          const Text(
            'Choisissez d\'où vous voulez sélectionner votre photo de profil',
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          SizedBox(
            width: 300,
            child: TextButton.icon(
              onPressed: () => onSourceSelected(ImageSource.camera),
              icon: const Icon(Icons.camera_alt),
              label: const Text('Caméra'),
            ),
          ),
          const SizedBox(height: 10),
          SizedBox(
            width: 300,
            child: TextButton.icon(
              onPressed: () => onSourceSelected(ImageSource.gallery),
              icon: const Icon(Icons.photo_library),
              label: const Text('Galerie'),
            ),
          ),
          const SizedBox(height: 10),
          SizedBox(
            width: 300,
            child: OutlinedButton(
              onPressed: onCancel,
              style: OutlinedButton.styleFrom(
                side: const BorderSide(color: AppColorSchemes.primaryBlue),
                backgroundColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(5),
                ),
              ),
              child: const Text('Annuler'),
            ),
          ),
        ],
      ),
    );
  }
}