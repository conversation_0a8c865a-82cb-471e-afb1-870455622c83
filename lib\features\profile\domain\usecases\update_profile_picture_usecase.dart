import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/profile_repository.dart';

/// Use case for updating user profile picture
class UpdateProfilePictureUseCase implements UseCase<void, UpdateProfilePictureParams> {
  final ProfileRepository repository;

  UpdateProfilePictureUseCase({required this.repository});

  @override
  Future<Either<Failure, void>> call(UpdateProfilePictureParams params) async {
    return await repository.updateProfilePicture(
      codeEtab: params.codeEtab,
      telephone: params.telephone,
      codeUtilisateur: params.codeUtilisateur,
      codeEtudiant: params.codeEtudiant,
      profilePicture: params.profilePicture,
    );
  }
}

/// Parameters for updating profile picture
class UpdateProfilePictureParams extends Equatable {
  final String codeEtab;
  final String telephone;
  final String? codeUtilisateur;
  final String codeEtudiant;
  final String profilePicture;

  const UpdateProfilePictureParams({
    required this.codeEtab,
    required this.telephone,
    this.codeUtilisateur,
    required this.codeEtudiant,
    required this.profilePicture,
  });

  @override
  List<Object?> get props => [
        codeEtab,
        telephone,
        codeUtilisateur,
        codeEtudiant,
        profilePicture,
      ];

  /// Convert to JSON for API request
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {
      'codeEtab': codeEtab,
      'telephone': telephone,
      'codeEtudiant': codeEtudiant,
      'profilePicture': profilePicture,
    };

    // Add codeUtilisateur if provided (for PAR profile)
    if (codeUtilisateur != null && codeUtilisateur!.isNotEmpty) {
      json['codeUtilisateur'] = codeUtilisateur;
    }

    return json;
  }
}
