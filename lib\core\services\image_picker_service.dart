import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:kairos/core/widgets/image_source_bottom_sheet.dart';

/// Service for handling image selection and conversion
class ImagePickerService {
  final ImagePicker _picker = ImagePicker();

  /// Show image source selection dialog and pick image
  Future<String?> pickAndConvertImage(BuildContext context) async {
    try {
      // Show dialog to choose image source
      final ImageSource? source = await _showImageSourceDialog(context);
      if (source == null) return null;

      // Pick image from selected source
      final XFile? pickedFile = await _picker.pickImage(
        source: source,
        maxWidth: 800, // Limit image size for better performance
        maxHeight: 800,
        imageQuality: 85, // Compress image to reduce file size
      );

      if (pickedFile == null) return null;

      // Convert image to base64
      final File imageFile = File(pickedFile.path);
      final List<int> imageBytes = await imageFile.readAsBytes();
      final String base64Image = base64Encode(imageBytes);

      // Add data URL prefix for proper base64 format
      final String mimeType = _getMimeType(pickedFile.path);
      return 'data:$mimeType;base64,$base64Image';

    } catch (e) {
      debugPrint('ImagePickerService: Error picking image: $e');
      return null;
    }
  }

  /// Show dialog to select image source (camera or gallery)
  Future<ImageSource?> _showImageSourceDialog(BuildContext context) async {
    return showModalBottomSheet<ImageSource>(
      context: context,
      builder: (BuildContext context) {
        return ImageSourceBottomSheet(
          onSourceSelected: (source) {
            Navigator.of(context).pop(source);
          },
          onCancel: () {
            Navigator.of(context).pop();
          },
        );
      },
    );
  }

  /// Get MIME type based on file extension
  String _getMimeType(String filePath) {
    final String extension = filePath.toLowerCase().split('.').last;
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      default:
        return 'image/jpeg'; // Default to JPEG
    }
  }

}
