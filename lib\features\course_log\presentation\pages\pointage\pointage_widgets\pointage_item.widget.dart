import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:kairos/features/course_log/domain/entities/course_log_entity.dart';

/// Widget that displays a single pointage entry
class PointageItem extends StatelessWidget {
  final CourseLogEntity pointage;

  const PointageItem({
    super.key,
    required this.pointage,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(5),
        border: Border.all(color: Colors.black, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.25),
            offset: const Offset(0, 4),
            blurRadius: 4,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Course name
            Text(
              pointage.cours,
              style: const TextStyle(
                fontFamily: 'Inter',
                fontWeight: FontWeight.w700,
                fontSize: 12,
                color: Colors.black,
              ),
            ),
            
            const SizedBox(height: 8),
            
            // Time, duration, and status row
            Row(
              children: [
                // Time range
                Text(
                  "${pointage.heureDebutPrevu} - ${pointage.heureFinPrevu} |",
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w600,
                    fontSize: 10,
                    color: Colors.black.withOpacity(0.8),
                  ),
                ),
                
                const SizedBox(width: 8),
                
                // Duration with clock icon
                Row(
                  children: [
                    SvgPicture.asset(
                      'assets/icons/icone_planning_cours.svg', // Clock icon
                      width: 17,
                      height: 14,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _calculateDuration(),
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w600,
                        fontSize: 10,
                        color: Colors.black.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
                
                const Spacer(),
                
                // Status
                Text(
                  _getStatusText(),
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w600,
                    fontSize: 10,
                    color: Colors.black.withOpacity(0.8),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            // Class information
            Text(
              "LPTI | Licence 3 | LPTI 1A | Semestre 1", // Mock class info
              style: const TextStyle(
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w500,
                fontSize: 10,
                letterSpacing: 0.025,
                color: Color(0xFF434545),
              ),
            ),
            
            const SizedBox(height: 4),
            
            // Registration date
            Text(
              "Enregistré le ${_formatRegistrationDate()}",
              style: TextStyle(
                fontFamily: 'Inter',
                fontWeight: FontWeight.w400,
                fontSize: 9,
                color: Colors.black.withOpacity(0.5),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _calculateDuration() {
    // Parse time strings and calculate duration
    try {
      final startParts = pointage.heureDebutPrevu.split(':');
      final endParts = pointage.heureFinPrevu.split(':');
      
      final startMinutes = int.parse(startParts[0]) * 60 + int.parse(startParts[1]);
      final endMinutes = int.parse(endParts[0]) * 60 + int.parse(endParts[1]);
      
      final durationMinutes = endMinutes - startMinutes;
      final hours = durationMinutes ~/ 60;
      final minutes = durationMinutes % 60;
      
      if (minutes == 0) {
        return "${hours}H";
      } else if (minutes == 30) {
        return "${hours}H30";
      } else {
        return "${hours}H${minutes}";
      }
    } catch (e) {
      return "2H"; // Default fallback
    }
  }

  String _getStatusText() {
    // Mock status logic - in real implementation this would come from the entity
    // For now, alternating between "Validé" and "Décompté" based on course name
    if (pointage.cours.hashCode % 2 == 0) {
      return "Statut : Validé";
    } else {
      return "Statut : Décompté";
    }
  }

  String _formatRegistrationDate() {
    try {
      final date = DateTime.parse(pointage.dateCours);
      final nextDay = date.add(const Duration(days: 1));
      return "${nextDay.day.toString().padLeft(2, '0')}/${nextDay.month.toString().padLeft(2, '0')}/${nextDay.year}";
    } catch (e) {
      return "18/04/2025"; // Default fallback
    }
  }
}
