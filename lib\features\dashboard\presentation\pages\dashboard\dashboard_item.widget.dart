import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:kairos/features/absences/presentation/pages/absences_retards/absences_retards.page.dart';
import 'package:kairos/features/absences/presentation/bloc/absences_retards_cubit.dart';
import 'package:kairos/features/course_log/presentation/pages/cahier_texte/cahier_texte.page.dart';
import 'package:kairos/features/course_log/presentation/pages/pointage/pointage.page.dart';
import 'package:kairos/features/student_records/presentation/pages/dossiers_etudiant/dossiers.page.dart';
import 'package:kairos/features/schedule/presentation/pages/emploi_du_temps/emploi_du_temps.page.dart';
import 'package:kairos/features/schedule/presentation/bloc/schedule_cubit.dart';
import 'package:kairos/features/finances/presentation/pages/finances/finances.page.dart';
import 'package:kairos/features/profile/presentation/pages/carte_virtuelle/carte_virtuelle.page.dart';
import 'package:kairos/features/grades/presentation/pages/notes/notes.page.dart';
import 'package:kairos/core/widgets/common/snackbar_widget.dart';
import 'package:kairos/core/constants/dashboard_strings.dart';
import 'package:kairos/features/dashboard/data/dashboard_item_type.enum.dart';
import 'package:kairos/core/utils/navigation_utils.dart';
import 'package:page_transition/page_transition.dart';
import 'package:kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart';
import 'package:kairos/features/grades/presentation/bloc/notes_cubit.dart';
import 'package:kairos/features/finances/presentation/bloc/finances_cubit.dart';
import 'package:kairos/core/di/injection_container.dart';
import 'package:kairos/features/course_log/presentation/bloc/course_log_cubit.dart';
import 'package:kairos/features/student_records/presentation/bloc/dossiers_cubit.dart';
import 'package:kairos/features/educational_resources/presentation/bloc/ressources_pedagogiques_cubit.dart';
import 'package:kairos/features/educational_resources/presentation/pages/ressources_pedagogiques.page.dart';
import 'package:kairos/features/finances/presentation/pages/controle_financier/controle_financier.page.dart';
import 'package:kairos/features/schools/presentation/bloc/financial_status_cubit.dart';
import 'package:kairos/core/services/qr_scanner_service.dart';

class DashboardItem extends StatelessWidget {
    final dynamic title;
    final dynamic subtitle;
    final dynamic iconName;
    final DashboardItemType? itemType;
    final EtablissementUtilisateur school;
    final EnfantTuteurEntity? etudiant;

    const DashboardItem({
        super.key,
        required this.title,
        this.subtitle,
        required this.iconName,
        this.itemType,
        required this.school,
        this.etudiant,
    });

    @override
    Widget build(BuildContext context) {
        return Container(
            margin: const EdgeInsets.symmetric(vertical: 5.0, horizontal: 7.0),
            decoration:  BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Theme.of(context).colorScheme.secondary, width: 1),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.25),
                offset: const Offset(0, 4),
                blurRadius: 4,
                spreadRadius: 0,
              ),
            ],
          ),
            child: ListTile(
                contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                title: title is Widget ? DefaultTextStyle.merge(style: const TextStyle(fontSize: 11), child: title) : Text(title, style: const TextStyle(fontSize: 11)),
                subtitle: subtitle != null && subtitle is String ? Text(subtitle, style: const TextStyle(fontSize: 10)): subtitle != null && subtitle is Widget ? subtitle: null,
                leading: SvgPicture.asset("assets/icons/$iconName", width: 33, height: 33, fit: BoxFit.fill),
                trailing: const Icon(Icons.arrow_forward_ios, size: 10, weight: 900,),
                onTap: () {
                    if (itemType != null) {
                        switch (itemType!) {
                            case DashboardItemType.notes:
                                NavigationUtils.push(
                                    context,
                                    BlocProvider(
                                        create: (context) => sl<NotesCubit>(),
                                        child: NotesPage(school: school, etudiant: etudiant),
                                    ),
                                    type: PageTransitionType.rightToLeft,
                                );
                                break;
                            case DashboardItemType.finances:
                                NavigationUtils.push(
                                    context,
                                    BlocProvider(
                                        create: (context) => sl<FinancesCubit>(),
                                        child: FinancesPage(school: school, etudiant: etudiant),
                                    ),
                                    type: PageTransitionType.rightToLeft,
                                );
                                break;
                            case DashboardItemType.absences:
                                NavigationUtils.push(
                                    context,
                                    BlocProvider(
                                        create: (context) => sl<AbsencesRetardsCubit>(),
                                        child: AbsencesRetardsPage(school: school, etudiant: etudiant),
                                    ),
                                    type: PageTransitionType.rightToLeft,
                                );
                                break;
                            case DashboardItemType.dossiers:
                                NavigationUtils.push(
                                    context,
                                    BlocProvider(
                                        create: (context) => sl<DossiersCubit>(),
                                        child: DossiersPage(school: school, etudiant: etudiant),
                                    ),
                                    type: PageTransitionType.rightToLeft,
                                );
                                break;
                            case DashboardItemType.planning:
                                NavigationUtils.push(
                                    context,
                                    BlocProvider(
                                        create: (context) => sl<EmploiDuTempsCubit>(),
                                        child: EmploiDuTempsPage(school: school, etudiant: etudiant),
                                    ),
                                    type: PageTransitionType.rightToLeft,
                                );
                                break;
                            case DashboardItemType.cahierTexte:
                                NavigationUtils.push(
                                    context,
                                    BlocProvider(
                                        create: (context) => sl<CourseLogCubit>(),
                                        child: CahierTextePage(school: school, etudiant: etudiant),
                                    ),
                                    type: PageTransitionType.rightToLeft,
                                );
                                break;
                            case DashboardItemType.pointage:
                                NavigationUtils.push(
                                    context,
                                    BlocProvider(
                                        create: (context) => sl<CourseLogCubit>(),
                                        child: PointagePage(school: school, etudiant: etudiant),
                                    ),
                                    type: PageTransitionType.rightToLeft,
                                );
                                break;
                            case DashboardItemType.controleFinancier:
                                _handleFinancialControl(context, school, etudiant);
                                break;
                            case DashboardItemType.ressources:
                                NavigationUtils.push(
                                    context,
                                    BlocProvider(
                                        create: (context) => sl<RessourcesPedagogiquesCubit>(),
                                        child: RessourcesPedagogiquesPage(school: school, etudiant: etudiant),
                                    ),
                                    type: PageTransitionType.rightToLeft,
                                );
                                break;
                            case DashboardItemType.carteVirtuelle:
                                NavigationUtils.push(
                                    context,
                                    CarteVirtuellePage(school: school, etudiant: etudiant),
                                    type: PageTransitionType.rightToLeft,
                                );
                                break;
                        }
                    } else {
                        // Handle string-based navigation
                        switch (title) {
                            case DashboardStrings.notesTitle:
                                NavigationUtils.push(
                                    context,
                                    BlocProvider(
                                        create: (context) => sl<NotesCubit>(),
                                        child: NotesPage(school: school, etudiant: etudiant),
                                    ),
                                    type: PageTransitionType.rightToLeft,
                                );
                                break;
                            case DashboardStrings.financesTitle:
                                NavigationUtils.push(
                                    context,
                                    BlocProvider(
                                        create: (context) => sl<FinancesCubit>(),
                                        child: FinancesPage(school: school, etudiant: etudiant),
                                    ),
                                    type: PageTransitionType.rightToLeft,
                                );
                                break;
                            case DashboardStrings.absencesTitle:
                                NavigationUtils.push(
                                    context,
                                    BlocProvider(
                                        create: (context) => sl<AbsencesRetardsCubit>(),
                                        child: AbsencesRetardsPage(school: school, etudiant: etudiant),
                                    ),
                                    type: PageTransitionType.rightToLeft,
                                );
                                break;
                            case DashboardStrings.dossiersTitle:
                                NavigationUtils.push(
                                    context,
                                    BlocProvider(
                                        create: (context) => sl<DossiersCubit>(),
                                        child: DossiersPage(school: school, etudiant: etudiant),
                                    ),
                                    type: PageTransitionType.rightToLeft,
                                );
                                break;
                            case DashboardStrings.cahierTitle:
                                NavigationUtils.push(
                                    context,
                                    BlocProvider(
                                        create: (context) => sl<CourseLogCubit>(),
                                        child: CahierTextePage(school: school, etudiant: etudiant),
                                    ),
                                    type: PageTransitionType.rightToLeft,
                                );
                                break;
                            case DashboardStrings.planningTitle:
                                NavigationUtils.push(
                                    context,
                                    BlocProvider(
                                        create: (context) => sl<EmploiDuTempsCubit>(),
                                        child: EmploiDuTempsPage(school: school, etudiant: etudiant),
                                    ),
                                    type: PageTransitionType.rightToLeft,
                                );
                                break;
                            case DashboardStrings.virtualCardTitle:
                                NavigationUtils.push(
                                    context,
                                    CarteVirtuellePage(school: school, etudiant: etudiant),
                                    type: PageTransitionType.rightToLeft,
                                );
                                break;
                            default:
                                ScaffoldMessenger.of(context).showSnackBar(
                                  CustomSnackbar(message: "Fonctionnalité en cours de développement").getSnackBar()
                              );
                                break;
                        }
                    }
                },
            ),
        );
    }

    /// Handle financial control with QR code scanning
    void _handleFinancialControl(
        BuildContext context,
        EtablissementUtilisateur school,
        EnfantTuteurEntity? etudiant,
    ) async {
        await QRScannerService.launchScanner(
            context: context,
            onQRScanned: (matricule) {
                // Handle QR code scan result - navigate with extracted matricule
                _navigateToControleFinancier(context, school, matricule);
            },
            onManualInput: (matricule) {
                // Handle manual input result - navigate with entered matricule
                _navigateToControleFinancier(context, school, matricule);
            },
            onError: () {
                if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                        CustomSnackbar(message: 'Erreur lors du contrôle. Veuillez réessayer.', isError: true).getSnackBar(),
                    );
                }
            },
        );
    }

    /// Navigate to ControleFinancierPage with the provided matricule
    void _navigateToControleFinancier(
        BuildContext context,
        EtablissementUtilisateur school,
        String matricule,
    ) {
        NavigationUtils.push(
            context,
            MultiBlocProvider(
                providers: [
                    BlocProvider(
                        create: (context) => sl<FinancesCubit>(),
                    ),
                    BlocProvider(
                        create: (context) => sl<FinancialStatusCubit>(),
                    ),
                ],
                child: ControleFinancierPage(
                    school: school,
                    matricule: matricule,
                ),
            ),
            type: PageTransitionType.rightToLeft,
        );
    }
}